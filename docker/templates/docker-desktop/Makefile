all: up
.PHONY: all up stop install install-php install-front install-admin install-admin-new build-front build-admin build-admin-new migrate migrations-reset xdebug-on xdebug-off

ROOT_DIR := $(strip $(shell dirname "$(realpath $(firstword $(MAKEFILE_LIST)))"))

up:
	docker compose up -d


stop:
	docker compose stop

install: install-php install-front install-admin install-admin-new

install-php: up
	docker compose run --rm -it app php composer.phar install

install-front: up
	docker compose run --rm -it front npm install

install-admin: src/admin/package.json src/admin/package-lock.json
	docker run --rm -it -v $(ROOT_DIR):/app -w /app/src/admin node:10 npm install

install-admin-new: up
	docker compose run --rm -it admin npm install

build: build-front build-admin build-admin-new

build-front: install-front
	docker compose run --rm -it front npm run build

build-admin: install-admin
	docker run --rm -it -v $(ROOT_DIR):/app -w /app/src/admin node:10 npx gulp

build-admin-new: install-admin-new
	docker compose run --rm -it admin npm run build

migrate: install-php
	docker compose run --rm -it app php bin/console migrations:continue

migrations-reset: install-php
	docker compose run --rm -it app php bin/console migrations:reset

populate-elastic: install-php
	docker compose run --rm -it app php bin/console elastic:index:create -psc

xdebug-on:
	SUPERADMIN_XDEBUG=on docker compose up --force-recreate --no-deps -d app

xdebug-off:
	SUPERADMIN_XDEBUG=off docker compose up --force-recreate --no-deps -d app

clear-elastic: install-php
	docker compose run --rm -it app php bin/console elastic:index:purge -f

redis-clear-front:
	docker exec -i dronpro_redis sh -c "redis-cli -n 2 FLUSHDB"
redis-clear-storage:
	docker exec -i dronpro_redis sh -c "redis-cli -n 0 FLUSHDB"

supervisor-start:
	docker exec -i dronpro_app sh -c "service supervisor start"
supervisor-stop:
	docker exec -i dronpro_app sh -c "service supervisor stop"

import-db:
	docker exec -i dronpro_db mysql -u root -proot dronpro < ./dump.sql
export-db:
	docker exec -i dronpro_db sh -c 'exec mysqldump -uroot -proot dronpro' > local_dump.sql
php-console:
	docker exec -it dronpro_app bash
qa:
	docker compose run --rm -it app sh -c "\
                           php composer.phar phpstan && \
                           php composer.phar cs-fix && \
                           php composer.phar cs && \
                           php composer.phar tests"

cypress:
	docker-compose exec cypress npx cypress open

clean-cache:
	docker compose exec app php bin/console contributte:cache:clean
