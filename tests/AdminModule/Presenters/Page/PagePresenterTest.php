<?php

declare(strict_types=1);

namespace App\Tests\AdminModule\Presenters\Page;

use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Orm;
use App\Tests\Helpers\ContainerFactory;
use Nette\Application\IPresenterFactory;
use Nette\Application\Request;
use Nette\Application\Responses\RedirectResponse;
use Nette\Application\Responses\TextResponse;
use Nette\Application\UI\Presenter;
use Nette\Security\SimpleIdentity;
use Nette\Security\User;
use Tester\Assert;
use Tester\TestCase;

$containerFactory = require __DIR__ . '/../../../bootstrap-integration.php';

/**
 * @testCase
 * @skip
 */
final class PagePresenterTest extends TestCase
{
	const ROOT_PAGE_ID = 1;

	public function __construct(
		private readonly ContainerFactory $containerFactory,
	)
	{
	}

	public function testDefaultPageLoad(): void
	{
		$container = $this->containerFactory->createContainer();

		$mutation = $container->getByType(Orm::class)->mutation->getDefault();
		$container->getByType(MutationHolder::class)->setMutation($mutation);

		$user = $container->getByType(User::class);
		$user->login(new SimpleIdentity(31, 'developer'));

		$presenterFactory = $container->getByType(IPresenterFactory::class);
		$presenter = $presenterFactory->createPresenter('Page:Admin:Page');
		$presenter->autoCanonicalize = false;

		$request = new Request('Page:Admin:Page', 'GET', [Presenter::ACTION_KEY => Presenter::DEFAULT_ACTION, 'id' => self::ROOT_PAGE_ID]);
		$response = $presenter->run($request);

		Assert::type(TextResponse::class, $response);
		$html = (string) $response->getSource();

		$find = 'Obsah';
		Assert::true(str_contains($html, $find), sprintf('Page has toggle tab "content" \'%s\'', $find));
	}

}

(new PagePresenterTest($containerFactory))->run();
