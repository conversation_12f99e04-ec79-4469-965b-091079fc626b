<?php

declare(strict_types=1);

namespace App\Tests\AdminModule\Presenters\Homepage;

use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Orm;
use App\Tests\Helpers\ContainerFactory;
use Nette\Application\IPresenterFactory;
use Nette\Application\Request;
use Nette\Application\Responses\RedirectResponse;
use Nette\Application\Responses\TextResponse;
use Nette\Application\UI\Presenter;
use Nette\Security\SimpleIdentity;
use Nette\Security\User;
use Tester\Assert;
use Tester\TestCase;

$containerFactory = require __DIR__ . '/../../../bootstrap-integration.php';

/**
 * @testCase
 */
final class HomepagePresenterTest extends TestCase
{
	public function __construct(
		private readonly ContainerFactory $containerFactory,
	)
	{
	}

	public function testUnauthenticated(): void
	{
		$container = $this->containerFactory->createContainer();

		$presenterFactory = $container->getByType(IPresenterFactory::class);
		$presenter = $presenterFactory->createPresenter('Admin:Homepage');
		$presenter->autoCanonicalize = false;

		$request = new Request('Admin:Homepage', 'GET', [Presenter::ACTION_KEY => Presenter::DEFAULT_ACTION]);
		$response = $presenter->run($request);
		Assert::type(RedirectResponse::class, $response);
		Assert::match('%a%sign%a%', $response->getUrl());
	}

	public function testAuthenticated(): void
	{
		$container = $this->containerFactory->createContainer();

		$mutation = $container->getByType(Orm::class)->mutation->getDefault();
		$container->getByType(MutationHolder::class)->setMutation($mutation);

		$user = $container->getByType(User::class);
		$user->login(new SimpleIdentity(31, 'developer'));

		$presenterFactory = $container->getByType(IPresenterFactory::class);
		$presenter = $presenterFactory->createPresenter('Admin:Homepage');
		$presenter->autoCanonicalize = false;

		$request = new Request('Admin:Homepage', 'GET', [Presenter::ACTION_KEY => Presenter::DEFAULT_ACTION]);
		$response = $presenter->run($request);
		Assert::type(RedirectResponse::class, $response);
		Assert::match('%a%page%a%', $response->getUrl());
	}
}

(new HomepagePresenterTest($containerFactory))->run();
