<?php

declare(strict_types=1);

namespace App\Tests\Helpers;

use Closure;
use Nette\DI\Container;

final class ContainerFactory
{

	/**
	 * @param Closure(string|array $additionalConfig = []): Container $factory
	 */
	public function __construct(
		private readonly Closure $factory,
	)
	{
	}

	public function createContainer(string|array $additionalConfig = []): Container
	{
		return ($this->factory)($additionalConfig);
	}

}
