<?php

declare(strict_types=1);

namespace App\Tests\Helpers\Fake;

final class SessionSection extends \Nette\Http\SessionSection
{
	private array $data = [];

	public function __construct(\Nette\Http\Session $session, $name)
	{
		parent::__construct($session, $name);
	}

	public function getIterator(): \Iterator
	{
		return new \ArrayIterator($this->data);
	}

	public function __set(string $name, $value): void
	{
		$this->data[$name] = $value;
	}

	public function &__get(string $name): mixed
	{
		if ($this->warnOnUndefined && !array_key_exists($name, $this->data)) {
			trigger_error("The variable '$name' does not exist in session section", E_USER_NOTICE);
		}

		return $this->data[$name];
	}

	public function __isset(string $name): bool
	{
		return isset($this->data[$name]);
	}

	public function __unset(string $name): void
	{
		unset($this->data[$name]);
	}

	public function setExpiration($time, $variables = NULL): static
	{
		return $this;
	}

	public function removeExpiration($variables = NULL): void
	{
	}

	public function remove($name = null): void
	{
		$this->data = [];
	}
}
