<?php

declare(strict_types=1);

namespace App\Tests\FrontModule\Presenters\Page;

use App\Model\Orm\Orm;
use App\Tests\Helpers\ContainerFactory;
use Nette\Application\IPresenterFactory;
use Nette\Application\Request;
use Nette\Application\Responses\TextResponse;
use Nette\Application\UI\Presenter;
use Tester\Assert;
use Tester\DomQuery;
use Tester\TestCase;

$containerFactory = require __DIR__ . '/../../../bootstrap-integration.php';

/**
 * @testCase
 */
final class PagePresenterTest extends TestCase
{
	public function __construct(
		private readonly ContainerFactory $containerFactory,
	)
	{
	}

	public function testContact(): void
	{
		$container = $this->containerFactory->createContainer();

		$treeRepository = $container->getByType(Orm::class)->tree;
		$contactPage = $treeRepository->getById(8);

		$presenterFactory = $container->getByType(IPresenterFactory::class);
		$presenter = $presenterFactory->createPresenter('Front:Page');
		$presenter->autoCanonicalize = false;

		$request = new Request('Front:Page', 'GET', [
			Presenter::ACTION_KEY => 'contact',
			'object' => $contactPage,
			'idref' => $contactPage->id,
		]);

		$response = $presenter->run($request);
		Assert::type(TextResponse::class, $response);

		$html = (string) $response->getSource();
		$dom = DomQuery::fromHtml($html);

		Assert::true($dom->has('form.f-contact'), 'Contact page contains contact form');
	}
}

(new PagePresenterTest($containerFactory))->run();
