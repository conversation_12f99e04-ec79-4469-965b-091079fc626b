extensions:
	cf: App\Tests\Helpers\VoidExtension
	cc: App\Tests\Helpers\VoidExtension

	cfTest: App\Model\CustomField\CustomFieldsExtension
	ccTest: App\Model\CustomContent\CustomContentExtension

cfTest:
	definitions:
		text:
			type: text
			label: Text
			minLength: 42

		suggestRef:
			type: suggest
			subType: tree
			label: Suggest with referenced URL
			url: @cfTest.suggestUrls.foo

		suggestInline:
			type: suggest
			subType: tree
			label: Suggest with inline URL
			url:
				searchParameterName: search
				link: /bar

		suggestExtended:
			extends: @cfTest.definitions.suggestRef
			label: Suggest with extends

		group:
			type: group
			label: Group
			items:
				text: {type: text, label: Text}
				suggest: @cfTest.definitions.suggestRef

		list:
			type: list
			label: List
			items:
				text: {type: text, label: Text}
				suggest: @cfTest.definitions.suggestInline

	fields:
		fieldRef: @cfTest.definitions.group

		fieldInline:
			type: text
			label: Inline field

		fieldExtended: @cfTest.definitions.suggestExtended

	suggestUrls:
		foo:
			searchParameterName: search
			link: /foo
			params:
				foo: 42

	templates:
		"Presenter:action": [
			@cfTest.fieldRef,
			@cfTest.fieldInline,
			fieldTotallyInline: {type: text, label: Totally inline field},
			@cfTest.fieldExtended
		]

ccTest:
	templates: []
