extensions:
	cf: App\Tests\Helpers\VoidExtension
	cc: App\Tests\Helpers\VoidExtension

	cfTest: App\Model\CustomField\CustomFieldsExtension
	ccTest: App\Model\CustomContent\CustomContentExtension

cfTest:
	fields:
		text:
			type: text
			label: Text
			minLength: 42

ccTest:
	definitions:
		text:
			type: group
			label: Text
			items:
				text:
					type: text
					label: Text

	components:
		componentRef:
			icon: bacon
			template: ref
			hotkey: true
			definition: @ccTest.definitions.text

		componentInline:
			icon: bacon
			template: inline
			category: Inline
			definition:
				type: group
				label: Text
				items:
					text: @cfTest.text

	templates:
		"Presenter:action": [@ccTest.componentRef, @ccTest.componentInline]
		"Presenter:anotherAction": *
