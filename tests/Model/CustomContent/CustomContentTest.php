<?php

declare(strict_types=1);

namespace App\Tests\Model\CustomContent;

use App\Model\CustomContent\CustomContent;
use App\PostType\Page\Model\Orm\CommonTree;
use App\Tests\Helpers\ContainerFactory;
use Tester\Assert;
use Tester\TestCase;

$containerFactory = require __DIR__ . '/../../bootstrap-integration.php';

/**
 * @testCase
 */
final class CustomContentTest extends TestCase
{
	public function __construct(
		private readonly ContainerFactory $containerFactory,
	) {}

	public function testResolve(): void
	{
		$container = $this->containerFactory->createContainer(__DIR__ . '/config.neon');

		$customContent = $container->getByType(CustomContent::class);
		\assert($customContent instanceof CustomContent);

		$tree = new CommonTree();
		$tree->template = 'Presenter:action';
		$tree->injectCustomContent($customContent);
		$resolvedComponents = $customContent->resolveCustomComponentsFor($tree);
		Assert::count(2, $resolvedComponents);

		$componentRef = $resolvedComponents[0];
		Assert::same('{"icon":"bacon","template":"ref","hotkey":true,"scheme":{"type":"group","label":"Text","items":{"text":{"type":"text","label":"Text","order":0}},"draggable":true,"deletable":true}}', \json_encode($componentRef, \JSON_THROW_ON_ERROR));

		$componentInline = $resolvedComponents[1];
		Assert::same('{"icon":"bacon","template":"inline","category":"Inline","scheme":{"type":"group","label":"Text","items":{"text":{"type":"text","label":"Text","minLength":42,"order":0}},"draggable":true,"deletable":true}}', \json_encode($componentInline, \JSON_THROW_ON_ERROR));

		// resolve *
		$anotherTree = new CommonTree();
		$anotherTree->template = 'Presenter:anotherAction';
		$anotherTree->injectCustomContent($customContent);
		$resolvedComponents = $customContent->resolveCustomComponentsFor($anotherTree);
		Assert::count(2, $resolvedComponents);
	}

	public function testShow(): void
	{
		$container = $this->containerFactory->createContainer(__DIR__ . '/config.neon');

		$customContent = $container->getByType(CustomContent::class);
		\assert($customContent instanceof CustomContent);

		$tree = new CommonTree();
		$tree->injectCustomContent($customContent);
		$tree->template = 'Presenter:action';
		$tree->customContentJson = (object) [
			'ref____QWBrpyaLxnQm0xvEaq71D' => [
				(object) ['text' => 'Lorem ipsum dolor sit amet, consectetuer adipiscing elit.'],
			],
			'inline____ndpe1TcKTzGUON_sRj0xU' => [
				(object) ['text' => 'Other component.', 'extraField' => 'Extra field.'],
			],
		];

		$show = $customContent->prepareForToShow($tree, $tree->customContentJson);
		Assert::same('Lorem ipsum dolor sit amet, consectetuer adipiscing elit.', $show->ref____QWBrpyaLxnQm0xvEaq71D[0]->text);
		Assert::same('Other component.', $show->inline____ndpe1TcKTzGUON_sRj0xU[0]->text);
		Assert::true( ! isset($show->inline____ndpe1TcKTzGUON_sRj0xU[0]->extraField));
	}

	public function testSave(): void
	{
		$container = $this->containerFactory->createContainer(__DIR__ . '/config.neon');

		$customContent = $container->getByType(CustomContent::class);
		\assert($customContent instanceof CustomContent);

		$data = '{"ref____QWBrpyaLxnQm0xvEaq71D":[{"text":"Lorem ipsum dolor sit amet, consectetuer adipiscing elit."}],"inline____ndpe1TcKTzGUON_sRj0xU":[{"text":"Other component."}]}';
		$scheme = '{"ref____QWBrpyaLxnQm0xvEaq71D":{"icon":"bacon","template":"ref","hotkey":true,"scheme":{"type":"group","label":"Text","items":{"text":{"type":"text","label":"Text"}},"draggable":true,"deletable":true,"order":2}},"inline____ndpe1TcKTzGUON_sRj0xU":{"icon":"bacon","template":"inline","category":"Inline","scheme":{"type":"group","label":"Text","items":{"text":{"type":"text","label":"Text","minLength":42}},"draggable":true,"deletable":true,"order":1}}}';

		$savedData = $customContent->prepareDataToSave($data, $scheme);
		Assert::same('Lorem ipsum dolor sit amet, consectetuer adipiscing elit.', $savedData->ref____QWBrpyaLxnQm0xvEaq71D[0]->text);
		Assert::same('Other component.', $savedData->inline____ndpe1TcKTzGUON_sRj0xU[0]->text);
	}

	public function testOldComponent(): void
	{
		$container = $this->containerFactory->createContainer(__DIR__ . '/config.neon');

		$customContent = $container->getByType(CustomContent::class);
		\assert($customContent instanceof CustomContent);


		$tree = new CommonTree();
		$tree->injectCustomContent($customContent);
		$tree->template = 'Presenter:action';
		$tree->customContentJson = (object) [
			'ref____QWBrpyaLxnQm0xvEaq71D' => [
				(object) ['text' => 'Lorem ipsum dolor sit amet, consectetuer adipiscing elit.'],
			],
			'inline____ndpe1TcKTzGUON_sRj0xU' => [
				(object) ['text' => 'Other component.', 'extraField' => 'Extra field.'],
			],
			'oldComponentWithoutDefinition____ndpe1TcKTzGUON_sRj0xU' => [
				(object) ['text' => 'Other component.', 'extraField' => 'Extra field.'],
			],
		];

		$data = $tree->cc;
		Assert::true(!isset($data->oldComponentWithoutDefinition____ndpe1TcKTzGUON_sRj0xU));

		$treeWithEmmptyCC = new CommonTree();
		$treeWithEmmptyCC->injectCustomContent($customContent);
		$treeWithEmmptyCC->template = 'Presenter:action';
		$treeWithEmmptyCC->customContentJson = (object) [];
		$data = $treeWithEmmptyCC->cc;
		Assert::same([], (array) $data);
	}

}

(new CustomContentTest($containerFactory))->run();
