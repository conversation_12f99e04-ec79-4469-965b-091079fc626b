<?php declare(strict_types = 1);

namespace App\Tests\Model\Infrastructure\Latte;

use App\Infrastructure\Latte\Filters;
use Tester\Assert;
use Tester\TestCase;

require __DIR__ . '/../../../bootstrap.php';

/**
 * @testCase
 */
final class FiltersTest extends TestCase
{

	public function testVideoId(): void
	{
		$expectedVideoId = 'MRqger6VR4s';

		$videoUrl = sprintf('https://www.youtube.com/watch?v=%s', $expectedVideoId);
		$parsedVideoId = Filters::parseVideoId($videoUrl);
		Assert::equal($expectedVideoId, $parsedVideoId, 'Parsed youtube ID for base url');

		$videoUrl = sprintf('https://youtube.com/watch?v=%s', $expectedVideoId);
		$parsedVideoId = Filters::parseVideoId($videoUrl);
		Assert::equal($expectedVideoId, $parsedVideoId, 'Parsed youtube ID for base url without www');

		$videoUrl = sprintf('https://youtu.be/%s', $expectedVideoId);
		$parsedVideoId = Filters::parseVideoId($videoUrl);
		Assert::equal($expectedVideoId, $parsedVideoId, 'Parsed youtube ID for short');

		$videoUrl = sprintf('malformedUrl/%s', $expectedVideoId);
		$parsedVideoId = Filters::parseVideoId($videoUrl);
		Assert::null($parsedVideoId, 'Parsed youtube ID for malformed url');
	}

}

(new FiltersTest())->run();
