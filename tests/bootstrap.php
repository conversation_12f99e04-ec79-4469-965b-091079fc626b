<?php

declare(strict_types=1);

namespace App\Tests;

use Tester\Environment;
use Tester\Helpers;

require __DIR__ . '/../vendor/autoload.php';

Environment::setup();
\date_default_timezone_set('Europe/Prague');

\define('ROOT_DIR', \dirname(__DIR__));
\define('TEMP_DIR', ROOT_DIR . '/tests/temp/' . (isset($_SERVER['argv']) ? \md5(\serialize($_SERVER['argv'])) : \getmypid()));
Helpers::purge(\TEMP_DIR);
