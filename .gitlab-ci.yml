.php-cache: &php-cache
    key:
        prefix: php
        files:
            - composer.lock
    paths:
        - vendor/
    policy: pull
.php-prod-cache: &php-prod-cache
    key:
        prefix: php-prod
        files:
            - composer.lock
    paths:
        - vendor/
    policy: pull
.node-cache: &node-cache
    key:
        prefix: node
        files:
            - package-lock.json
    paths:
        - node_modules/
    policy: pull
.node-admin-cache: &node-admin-cache
    key:
        prefix: node-admin
        files:
            - src/admin/new/package-lock.json
    paths:
        - src/admin/new/node_modules
    policy: pull

default:
    image: $CI_REGISTRY/sk/docker-php:8.4
    interruptible: true
    tags:
        - arm
        - small

stages:
    - install
    - test
    - build
#    - e2e-deploy
#    - e2e-test
    - deploy

variables:
    GIT_DEPTH: 0
    FF_USE_FASTZIP: 1
    CACHE_COMPRESSION_LEVEL: 'fastest'

workflow:
    rules:
        -   if: $CI_PIPELINE_SOURCE == 'merge_request_event'
        -   if: $CI_COMMIT_BRANCH == 'master'
        -   if: $CI_COMMIT_TAG =~ /^prod-/
        -   if: $CI_COMMIT_TAG =~ /^test-/
        -   if: $CI_COMMIT_TAG =~ /^stage-/

###

.install.php:
    stage: install
    rules:
        -   changes:
                paths:
                    - composer.lock
                compare_to: master
        -   if: $CI_COMMIT_MESSAGE =~ /\[install\]/
    before_script:
        - php composer.phar config -g cache-dir "$(pwd)/.composer"
    tags:
        - arm
        - large

.install.node:
    stage: install
    script:
        - npm ci --cache .npm --prefer-offline --no-audit --fund=false
    tags:
        - x86
        - large


install.php.ci:
    extends: .install.php
    cache:
        <<: *php-cache
        policy: push
    script:
        - php composer.phar install --no-interaction --ansi


install.php.prod:
    extends: .install.php
    cache:
        <<: *php-prod-cache
        policy: push
    script:
        - php composer.phar install --no-interaction --ansi --no-dev --prefer-dist --optimize-autoloader

install.node.frontend:
    extends: .install.node
    image: public.ecr.aws/docker/library/node:18
    rules:
        -   changes:
                paths:
                    - package-lock.json
                compare_to: master
        -   if: $CI_COMMIT_MESSAGE =~ /\[install\]/
    cache:
        <<: *node-cache
        policy: push

install.node.admin:
    extends: .install.node
    image: public.ecr.aws/docker/library/node:14
    rules:
        -   changes:
                paths:
                    - src/admin/new/package-lock.json
                compare_to: master
        -   if: $CI_COMMIT_MESSAGE =~ /\[install\]/
    cache:
        <<: *node-admin-cache
        policy: push
    before_script:
        - cd src/admin/new

###

.test.php:
    stage: test
    cache:
        <<: *php-cache
    needs:
        -   job: install.php.ci
            optional: true


test.php.linter:
    extends: .test.php
    script: php composer.phar run-script lint

test.php.latte-linter:
    extends: .test.php
    script: php composer.phar run-script latte-lint

test.php.phpstan:
    extends: .test.php
    script: php composer.phar run-script phpstan
    tags:
        - arm
        - large

#test.php.tests:
#    extends: .test.php
#    services:
#        -   name: public.ecr.aws/docker/library/mariadb:10
#            alias: mysql
#            variables:
#                MYSQL_ROOT_PASSWORD: root
#                MYSQL_DATABASE: dobreknihy2023
#        -   name: redis:latest
#            alias: redis
#    before_script:
#        - cp tests/config.ci.neon app/config/config.local.neon
#        - php bin/console migrations:continue
#    script: php composer.phar run-script tests
#    tags: [ large, arm ]

test.php.coding-standard:
    extends: .test.php
    script: php composer.phar run-script cs

test.php.audit:
    extends: .test.php
    script: php composer.phar audit --locked --abandoned=ignore

###

.build:
    stage: build
    script:
        - npm run build
    tags:
        - x86
        - small

build.frontend:
    extends: .build
    image: public.ecr.aws/docker/library/node:18
    needs:
        -   job: install.node.frontend
            optional: true
    cache:
        <<: *node-cache
    artifacts:
        paths:
            - www/static
    only:
        - master
        - tags

build.admin:
    extends: .build
    image: public.ecr.aws/docker/library/node:14
    needs:
        -   job: install.node.admin
            optional: true
    cache:
        <<: *node-admin-cache
    before_script:
        - cd src/admin/new
    artifacts:
        paths:
            - www/admin/new/dist
    only:
        - master
        - tags

###

.deploy:
    stage: deploy
    image: $CI_REGISTRY/sk/docker-php-deployer:v7.3.3
    interruptible: false
    resource_group: deploy
    cache:
        <<: *php-prod-cache
    dependencies:
        - build.admin
        - build.frontend
    before_script:
        - mkdir -p ~/.ssh
        - chmod 700 ~/.ssh
        - cp "$SSH_KNOWN_HOSTS" ~/.ssh/known_hosts
        - chmod 644 ~/.ssh/known_hosts
        - cp "$DEPLOYMENT_KEY" ~/.ssh/id_ed25519
        - chmod 400 ~/.ssh/id_ed25519

    variables:
        supervisorConfPrefix: omega

deploy.staging:
    extends: .deploy
    rules:
        - if: $CI_COMMIT_BRANCH == 'master'

    environment:
        name: stage
        url: https://omega-stage.vs2.superkoderi.cz/
    script:
        - 'printf "parameters:\n\tconfig:\n\t\twebVersion: \"%s\"" $CI_COMMIT_SHORT_SHA > app/config/webVersion.neon'
        - dep -f .deploy.php deploy
    variables:
        supervisorConfDir: /etc/supervisor/conf.d

deploy.test:
    extends: .deploy
    rules:
        -   if: $CI_COMMIT_TAG =~ /^test-/
    environment:
        name: test
        url: https://omega-test.vs2.superkoderi.cz/
    script:
        - 'printf "parameters:\n\tconfig:\n\t\twebVersion: \"%s\"" $CI_COMMIT_TAG > app/config/webVersion.neon'
        - dep -f .deploy.php deploy

### production

.only.prod:
    rules:
        -   if: $CI_COMMIT_TAG =~ /^prod-/

#.e2ee2e:
#    rules:
#        - if: $CI_COMMIT_TAG =~ /^prod-/
#        - if: $CI_COMMIT_BRANCH == 'DK-808-cypress-tests'
#        - if: $CI_COMMIT_BRANCH == 'master'

#e2e.deploy:
#    extends:
#        - .deploy
#        - .e2e
#    stage: e2e-deploy
#    environment:
#        name: test
#        url: https://test.dobre-knihy.cz
#    variables:
#        resetDataStorage: 1
#    script:
#        - 'printf "parameters:\n\tconfig:\n\t\twebVersion: \"%s\"" $CI_COMMIT_TAG > app/config/webVersion.neon'
#        - dep -f .deploy.php deploy

#e2e.test:
#    extends: .e2e
#    stage: e2e-test
#    image:
#        name: cypress/included:cypress-13.15.0-node-20.17.0-chrome-129.0.6668.70-1-ff-130.0.1-edge-129.0.2792.52-1
#        entrypoint: [""]
#    tags: [large, x86]
#    needs:
#        - job: e2e.deploy
#        - job: install.node.frontend
#          optional: true
#    cache:
#        <<: *node-cache
#    variables:
#        CYPRESS_BASE_URL: "https://${BASIC_AUTH_CREDENTIALS}@test.dobre-knihy.cz"
#    script:
#        - npm run cy:run
#    artifacts:
#        when: on_failure
#        expire_in: 1 day
#        paths:
#            - cypress/screenshots

deploy.prod:
    extends:
        - .deploy
        - .only.prod
    environment:
        name: prod
        url: https://omega-prod.vs2.superkoderi.cz/
    script:
        - 'printf "parameters:\n\tconfig:\n\t\twebVersion: \"%s\"" $CI_COMMIT_TAG > app/config/webVersion.neon'
        - dep -f .deploy.php deploy
    variables:
        supervisorConfDir: /etc/supervisor/conf.d
###
