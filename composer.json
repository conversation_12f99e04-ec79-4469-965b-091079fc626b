{"name": "superkoders/superadmin", "type": "project", "require": {"php": "^8.4", "ext-fileinfo": "*", "ext-gd": "*", "ext-json": "*", "ext-simplexml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-amqp": "*", "azuyalabs/yasumi": "^2.3", "brick/money": "^0.8.0", "comgate/sdk": "^1.0", "composer/ca-bundle": "^1.3", "contributte/apitte": "^0.12.1", "contributte/application": "^0.5.1", "contributte/console": "^0.10.1", "contributte/console-extra": "^0.8.0", "contributte/elastica": "^2.0", "contributte/event-dispatcher": "^0.9.1", "contributte/event-dispatcher-extra": "^0.10.0", "contributte/logging": "^0.6.1", "contributte/messenger": "^0.1.0", "contributte/monolog": "^0.5.0", "contributte/redis": "^0.5.3", "contributte/validator": "^1.0", "cweagans/composer-patches": "^1.7", "dragonmantank/cron-expression": "^3.3", "endroid/qr-code": "^6.0", "firebase/php-jwt": "^6.1", "gopay/payments-sdk-php": "^1.10", "guzzlehttp/psr7": "^2.0", "heureka/overeno-zakazniky": "^4.0", "jaybizzle/crawler-detect": "^1.2", "latte/latte": "^3.0", "league/csv": "^9.6", "league/oauth2-google": "^4.0", "marc-mabe/php-enum": "^4.3", "mpdf/mpdf": "^8.2", "nette/application": "^3.1", "nette/bootstrap": "^3.1", "nette/caching": "^3.1", "nette/di": "^3.2", "nette/finder": "^3.0", "nette/forms": "^3.1", "nette/http": "^3.2", "nette/mail": "^4.0", "nette/php-generator": "^3.6 || ^4.0", "nette/robot-loader": "^3.4 || ^4.0", "nette/safe-stream": "^2.5 || ^3.0", "nette/security": "^3.1", "nette/tokenizer": "^3.1", "nette/utils": "^3.2 || ^4.0", "nettrine/annotations": "^0.7.0", "nettrine/cache": "^0.3.0", "nextras/migrations": "^3.1", "nextras/orm": "^5.0.0", "pelago/emogrifier": "^7.0", "php-curl-class/php-curl-class": "^9.0", "prewk/xml-string-streamer": "^1.2", "rikudou/czqrpayment": "^5.3", "rikudou/skqrpayment": "^4.2", "sentry/sdk": "^4.0", "symfony/amqp-messenger": "^7.2", "symfony/cache": "^6.4", "symfony/lock": "^6.0", "symfony/property-access": "^6.2", "symfony/redis-messenger": "^5.4", "symfony/scheduler": "^7.1", "symfony/validator": "^6.0", "texy/texy": "^3.1", "tracy/tracy": "^2.9", "ublaboo/datagrid": "^6.9.5", "venca-x/social-login": "^1.2", "webchemistry/oauth2-seznam": "1.0"}, "require-dev": {"deployer/deployer": "^7.0@rc", "mockery/mockery": "^1.4", "nette/tester": "^2.4", "nextras/orm-phpstan": "^2.0", "ninjify/coding-standard": "^0.12", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^2.0", "phpstan/phpstan-deprecation-rules": "^2.0", "phpstan/phpstan-nette": "^2.0", "roave/security-advisories": "dev-latest"}, "autoload": {"psr-4": {"App\\": ["app/"]}, "files": ["app/bd.php"]}, "autoload-dev": {"psr-4": {"App\\Tests\\": ["tests/"]}}, "config": {"platform": {"php": "8.4.1"}, "sort-packages": true, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "phpstan/extension-installer": true, "cweagans/composer-patches": true, "php-http/discovery": true}}, "scripts": {"tests": "tester -C -s tests/", "phpstan": "phpstan analyse -c .phpstan.neon --memory-limit=2G", "lint": "parallel-lint --blame app tests --exclude tests/var", "latte-lint": "latte-lint app", "cs": "phpcs --standard=.ruleset.xml", "cs-fix": "phpcbf --standard=.ruleset.xml", "qc": ["@lint", "@latte-lint", "@cs", "@phpstan", "@tests"], "commit": ["@cs-fix", "@cs", "@phpstan"]}, "extra": {"patches": {"contributte/logging": {"Loading integrations dumping": "patches/php84/contributte-logging-sentry-logger.patch"}, "nextras/orm": {"Added debug identity map": "patches/php84/nextras-orm-added-identity-map-debug.patch"}}}}